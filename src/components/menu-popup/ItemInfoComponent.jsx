'use client'
import ImageWrapperResponsive from '../ImageWrapperResponsive'
import { useEffect, useState } from 'react'
import { useContextExperience } from '@/contexts/useContextExperience'
import SpinerComponent from '../SpinerComponent'

function HtmlContentDisplay({htmlString}) {
  return (
    <div dangerouslySetInnerHTML={{ __html: htmlString }} />
  );
}


export default function ItemInfoComponent() {
  const [error,setError]=useState('')
  const [showError,setShowError]=useState(false)
  const [loading,setLoading]=useState(false)
  const [data,setData]=useState(null)
  const {experienceState,disptachExperience}=useContextExperience()

  const fetchData = async (id) => {
    try {
      setLoading(true)
      setError('')
      setShowError(false)

      const serverResponse = await fetch(`/api/info-markers/${id}`)

      // Check if response is ok
      if (!serverResponse.ok) {
        throw new Error(`HTTP ${serverResponse.status}: ${serverResponse.statusText}`)
      }

      // Check if response is JSON
      const contentType = serverResponse.headers.get('content-type')
      if (!contentType || !contentType.includes('application/json')) {
        const responseText = await serverResponse.text()
        console.error('Non-JSON response received:', responseText)
        throw new Error('Server returned non-JSON response. This might be a server error.')
      }

      const responseData = await serverResponse.json()
      // console.log('API Response:', responseData)

      // Check if the API response indicates success
      if (!responseData.success) {
        throw new Error(responseData.message || 'Failed to load data')
      }

      // Check if data exists in response
      if (!responseData.data) {
        throw new Error('No data found for this info marker')
      }

      setData(responseData.data)
      setLoading(false)
    } catch (error) {
      console.error('Error fetching info marker:', error)
      setError(error.message || 'Failed to load data')
      setShowError(true)
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData(experienceState?.showItemInfo?.id)
  }, [experienceState?.showItemInfo?.id])

  // console.log('ItemInfoComponent:',data)
  
  return (
    <div className='flex w-full h-fit text-white'>
      {loading 
        ? <div className='flex w-full h-full items-center justify-center'><SpinerComponent/></div>  
        : <div className='flex w-full h-full flex-col items-start justify-start mt-16'>
            {/* <ImageWrapperResponsive src={data?.image} className='w-full h-full'/> */}
            <div className='flex flex-col mb-20 w-full h-fit items-center justify-start gap-5 overflow-y-auto'>
              <div className='flex flex-col w-full h-fit items-center justify-start gap-10'>
                <div className='w-full h-fit relative'>
                  <img src={data?.image} alt='page image' className='object-cover h-auto w-full'/>
                </div>
                <div className='flex w-full h-fit gap-10 flex-col lg:flex-row'>
                  <h1 className='max-w-1/2 text-6xl text-left text-wrap leading-12'>
                    <HtmlContentDisplay htmlString={data?.title}/>
                  </h1>
                  <div className='flex flex-col max-w-full md:max-w-[676px] gap-10'>
                    <p className='text-left leading-7 text-3xl'>
                      <HtmlContentDisplay htmlString={data?.body1}/>
                    </p>
                    <p className='text-left wfull font-thin leading-7'>
                      <HtmlContentDisplay htmlString={data?.body2}/>
                    </p>
                  </div>
                </div>
                <div className='flex flex-col w-full h-fit mt-10 gap-20'>
                  {data?.secondaryEntries?.map((i,index)=>(
                    <div key={index} className='flex flex-col md:flex-row md:even:flex-row-reverse w-full h-fit items-start justify-start gap-10'>
                      <div className='h-fit md:h-[422px] max-w-full md:w-[474px] relative'>
                        <img src={i?.image} alt='page image' className='object-cover w-full h-auto'/>
                      </div>
                      <div className='flex max-w-full md:w-[calc(100%-474px)] flex-col h-fit gap-5'>
                        <h1 className='max-w-1/2 text-6xl text-left leading-12'>
                          {/* {i?.title} */}
                          <HtmlContentDisplay htmlString={i?.title}/>
                        </h1>
                        <div>
                          <p className='w-full text-left leading-7 text-3xl'>
                            {/* {i?.body1} */}
                            <HtmlContentDisplay htmlString={i?.body1}/>
                          </p>
                          <p className='w-full font-thin text-left leading-7'>
                            {/* {i?.body2} */}
                            <HtmlContentDisplay htmlString={i?.body2}/>
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
      }
    </div>
  )
}
