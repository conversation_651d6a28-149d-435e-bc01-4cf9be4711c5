import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import React from 'react'
import {APIProvider, Map} from '@vis.gl/react-google-maps';
import { useContextExperience } from '@/contexts/useContextExperience';

export default function PagePopupLocationAndContact({data}) {
  const {experienceState,disptachExperience}=useContextExperience()
  const handlePopupClose=()=>{
    // console.log('handlePopupCLose',experienceState?.showTheIslandPage)
    // console.log('handlePopupCLose',experienceState?.showExperiencePage)
    {experienceState?.showLocationAndContacts && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_LOCATION_AND_CONTACTS_PAGE})}
  }
  // const {image,body,title,secondaryEntries,url}=item
  const locationGPS={lat:-24.6424504,lng:25.8965668}
  const containerStyles={with:'100%',height:'100%'}
  const textList=[
    {title:'WHATSAPP',desc:'(+267) 72 808 308'},
    {title:'TELEPHONE',desc:'(+34) 72 808 308'},
    {title:'EMAIL',desc:'<EMAIL>'},
    {title:'ADDRESS',desc:`Boro River, Okavango,
            Maun, Botswana`
    },
    {title:'GPS CO-ORDINATES',desc:`19, 51'25, 2468 | S
            23, 26'7, 6056 | E`
    },
  ]
  const inputList=[
    {name:'name',type:'text'},
    {name:'email',type:'text'},
  ]
  return (
    <div className='popu-location-and-contactsflex z-30 absolute top-0 left-0 w-full h-full bg-black/75 overflow-hidden overflow-y-auto'>
      <div className='popu-island-experiences flex z-30 absolute top-0 left-0 w-full h-full bg-black/75 overflow-hidden overflow-y-auto text-white items-center justify-center'>
        <div className='popup-wrapper text-white flex z-10 absolute top-0 left-0 w-full h-full bg-black/85 overflow-y-auto'>
          <div 
            onClick={handlePopupClose} 
            className=" flex z-40 items-center justify-center absolute right-[104px] top-[0] h-[75px] w-[96px] cursor-pointer"
            >
            <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          </div>
          <div className='flex relative top-[75px] left-0 h-fit px-5 lg:w-[995px] mx-auto flex-col text-white mb-40'>
            <a href='https://www.google.com/maps/place/Elephant+Island+Lodge/@-24.6424504,25.8965668,17z/data=!3m1!4b1!4m6!3m5!1s0x1e95066666666666:0x666666666666' className='flex font-thin w-full h-auto items-center justify-center overflow-hidden'>
              <img src={'/assets/map_location_img_001.jpg'} alt='page image' className='object-cover h-auto w-full'/>
              {/* <APIProvider apiKey={process.env.GOOGLE_API_KEY}>
                <Map
                  style={{width: '100%', height: '100%'}}
                  defaultCenter={{lat: locationGPS.lat, lng: locationGPS.lng}}
                  defaultZoom={3}
                  gestureHandling={'greedy'}
                  disableDefaultUI={true}
                />
              </APIProvider> */}
            </a>
            <div className='flex mx-auto mt-7 max-w-full lg:max-w-[676px] h-full gap-10 flex-col lg:flex-row'>
              <div className='flex w-1/3 flex-col'>
                <h1 className='w-full text-3xl leading-6 font-bold text-left text-wrap uppercase mb-2'>
                  LOCATION & CONTACTS
                </h1>
                {textList?.map((i,index)=>
                  <div className='flex flex-col gap-1' key={index}>
                    <span className='font-bold uppercase'>{i?.title}</span>
                    <span className='text-sm'>{i?.desc}</span>
                  </div>
                )}
              </div>
              <div className='flex flex-col w-full'>
                <span>Do not hesitate to contact <span className='font-bold capitalize'>elephant island</span> by filling in the contact form below</span>
                <form className='flex mt-1 flex-col w-full gap-1' action="">
                  {inputList?.map((i,index)=>
                    <div key={index} className='flex flex-col w-full gap-1'>
                      <span className='text-sm uppercase placeholder:text-white'>name</span>
                      <input id={'name'} className='flex items-center h-10 gap-2 mt-0 border-6 border-white rounded-xl' placeholder='' type="text" />
                  </div>)}
                  <div className='flex flex-col w-full gap-1'>
                    <span className='text-sm uppercase placeholder:text-white'>name</span>
                    <textarea  minLength={500} rows={10} cols={5} id={'name'} className='flex items-center h-16 gap-2 mt-0 border-6 border-white rounded-xl' placeholder='' type="text" />
                  </div>
                  <input className='flex w-fit px-4 rounded-full h-7 bg-white text-gray-900 mt-2' type="button" value="send message" />
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
