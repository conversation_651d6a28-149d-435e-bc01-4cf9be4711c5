import { ACTIONS_EXPERIENCE_STATE } from '@/contexts/reducerExperience'
import { useContextExperience } from '@/contexts/useContextExperience'
import React from 'react'

export default function PopupTestimonials({data}) {
  const {experienceState,disptachExperience}=useContextExperience()
  const handlePopupClose=()=>{
    // console.log('handlePopupCLose',experienceState?.showTheIslandPage)
    // console.log('handlePopupCLose',experienceState?.showExperiencePage)
    {experienceState?.showLocationAndContacts && disptachExperience({type:ACTIONS_EXPERIENCE_STATE.SHOW_TESTIMONIALS_PAGE})}
  }
  console.log('PopupTestimonials:',data)
  return (
    <div className='popu-location-and-contactsflex z-30 absolute top-0 left-0 w-full h-full bg-black/75 overflow-hidden overflow-y-auto'>
      <div className='popu-island-experiences flex z-30 absolute top-0 left-0 w-full h-full bg-black/75 overflow-hidden overflow-y-auto text-white items-center justify-center'>
        <div className='popup-wrapper text-white flex z-10 absolute top-0 left-0 w-full h-full bg-black/85 overflow-y-auto'>
          <div 
            onClick={handlePopupClose} 
            className=" flex z-40 items-center justify-center absolute right-[104px] top-[0] h-[75px] w-[96px] cursor-pointer"
            >
            <div className={`rotate-45  bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
            <div className={`-rotate-45 bg-[#e7e0da] h-1 m-auto absolute w-10 duration-300 ease-linear`}/>
          </div>
          <div className='flex relative top-[75px] left-0 h-fit px-5 lg:w-[995px] mx-auto flex-col text-white mb-40'>
            <div className='flex w-full flex-col'>
              <h1 className='max-w-30 text-3xl leading-8 font-bold text-left text-wrap uppercase mb-2'>
                VISITOR TESTIMONIALS
              </h1>
              <p className='flex w-full text-sm'>
                What client's say...
              </p>
            </div>
            <div className='flex flex-col w-full h-fit gap-10'>
              {data?.testimonials?.map((i,index)=>(
                <div key={index} className='flex flex-col w-full h-fit gap-4'>
                  <div className='flex w-20 h-20 rounded-full overflow-hidden'>
                    <img src="/assets/testimonials_img_001.jpg" alt="" />
                  </div>
                  <h1 className='w-full text-4xl text-left leading-12 uppercase'>
                    {i?.name}
                  </h1>
                  <p className='w-full text-left leading-7'>
                    {i?.comment}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
